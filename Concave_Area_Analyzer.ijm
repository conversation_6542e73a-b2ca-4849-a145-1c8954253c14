// Concave Area Analyzer - ImageJ Macro
// Analyzes particles and counts distinct concave areas with their respective measurements
// Optimized for speed and comprehensive concavity analysis

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
minConcaveAreaPixels = 5;       // Minimum concave area to count (pixels)
maxParticles = 1000;            // Maximum particles to analyze
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// Results table names
summaryTableName = "Concave_Areas_Summary";
detailTableName = "Concave_Areas_Detail";

// Classification parameters
classificationMethod = "auto"; // "auto", "manual", or "percentile"
// Manual boundaries (used if classificationMethod = "manual")
manualSmallThreshold = 1.0;     // µm² - boundary between small and moderate
manualModerateThreshold = 5.0;  // µm² - boundary between moderate and large
// Percentile boundaries (used if classificationMethod = "percentile")
smallPercentile = 33.3;         // Bottom 33.3% = small (surface roughness)
moderatePercentile = 66.7;      // Middle 33.3% = moderate (waviness)
                               // Top 33.3% = large (satellites)

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;
pixelsPerMicron = pixelDistance / realDistance;
minParticleAreaMicrons = minParticleAreaPixels * micronsPerPixel * micronsPerPixel;
minConcaveAreaMicrons = minConcaveAreaPixels * micronsPerPixel * micronsPerPixel;

print("\\Clear");
print("=== CONCAVE AREA ANALYZER ===");
print("Image: " + originalTitle);
print("Calibration: " + d2s(micronsPerPixel, 4) + " " + unit + "/pixel");
print("Starting analysis...");

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// Set calibration using provided parameters
run("Set Scale...", "distance=" + pixelDistance + " known=" + realDistance + " unit=" + unit);

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area perimeter centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaMicrons + "-Infinity display exclude clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

if (nParticles > maxParticles) {
    exit("Too many particles (" + nParticles + "). Maximum allowed: " + maxParticles);
}

print("Found " + nParticles + " particles for analysis");

// Store basic particle measurements
particleAreas = newArray(nParticles);
particlePerimeters = newArray(nParticles);
particleCentroids = newArray(nParticles * 2); // x,y pairs

for (i = 0; i < nParticles; i++) {
    particleAreas[i] = getResult("Area", i);
    particlePerimeters[i] = getResult("Perim.", i);
    particleCentroids[i * 2] = getResult("X", i);
    particleCentroids[i * 2 + 1] = getResult("Y", i);
}

// =============================================================================
// CONCAVE AREA ANALYSIS
// =============================================================================

// Arrays to store results
concaveAreaCounts = newArray(nParticles);
totalConcaveAreas = 0;

// Detailed results arrays (will be resized as needed)
detailParticleIDs = newArray(0);
detailConcaveIDs = newArray(0);
detailAreas = newArray(0);
detailPerimeters = newArray(0);
detailCentroids = newArray(0); // Will store x,y pairs
detailSolidities = newArray(0);
detailCircularities = newArray(0);
detailAspectRatios = newArray(0);
detailClassifications = newArray(0); // Will store classification: 1=small, 2=moderate, 3=large

// Classification boundary variables (will be determined after analysis)
smallThreshold = 0;    // Boundary between small and moderate concavities
moderateThreshold = 0; // Boundary between moderate and large concavities

// Classification counters
totalSmallConcavities = 0;
totalModerateConcavities = 0;
totalLargeConcavities = 0;

print("Analyzing concave areas...");

// Enable batch mode for speed
setBatchMode(true);

// Main analysis loop
for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);
    
    // Get particle ROI bounds for creating minimal working image
    roiManager("select", p);
    Roi.getBounds(x, y, width, height);
    
    // Create minimal working image for this particle (with padding)
    padding = 10;
    newImage("ParticleWork", "8-bit black", width + 2*padding, height + 2*padding, 1);
    particleWorkID = getImageID();
    
    // Copy particle to smaller image
    selectImage(workingID);
    roiManager("select", p);
    run("Copy");
    selectImage(particleWorkID);
    makeRectangle(padding, padding, width, height);
    run("Paste");
    run("Select None");

    // Fill holes in the particle to avoid internal pores being mistaken for concavities
    setThreshold(128, 255);
    run("Create Selection");
    if (selectionType() != -1) {
        run("Fill Holes");
        run("Clear", "slice");
        setForegroundColor(255, 255, 255);
        run("Fill", "slice");
    }
    run("Select None");
    resetThreshold();
    
    // Create convex hull version
    selectImage(particleWorkID);
    run("Duplicate...", "title=ConvexHull");
    convexHullID = getImageID();
    
    // Find particle and create convex hull
    setThreshold(128, 255);
    run("Create Selection");
    if (selectionType() != -1) {
        run("Convex Hull");
        run("Clear", "slice");
        setForegroundColor(255, 255, 255);
        run("Fill", "slice");
    }
    run("Select None");
    resetThreshold();
    
    // Calculate concave regions (convex hull - original particle)
    imageCalculator("Subtract create", convexHullID, particleWorkID);
    concaveRegionsID = getImageID();
    
    // Clean up intermediate images
    selectImage(convexHullID);
    close();
    selectImage(particleWorkID);
    close();
    
    // Analyze individual concave areas using connected components
    selectImage(concaveRegionsID);

    // Set calibration on the concave regions image to ensure proper measurements
    run("Set Scale...", "distance=" + pixelDistance + " known=" + realDistance + " unit=" + unit);

    // Use "Analyze Particles" to find connected components in concave regions
    run("Set Measurements...", "area perimeter centroid bounding shape fit redirect=None decimal=3");
    run("Watershed");
    run("Analyze Particles...", "size=" + minConcaveAreaMicrons + "-Infinity display clear");

    nConcaveAreas = nResults;
    concaveAreaCounts[p] = nConcaveAreas;
    totalConcaveAreas += nConcaveAreas;

    // Populate the global detail arrays directly in main loop
    for (i = 0; i < nConcaveAreas; i++) {
        // Get measurements (now in calibrated units due to calibration set above)
        area = getResult("Area", i);
        perimeter = getResult("Perim.", i);

        // Convert centroid coordinates: from concave image pixels to original image calibrated units
        centroidXPixels = getResult("X", i) + x - padding; // Adjust for padding and offset to original image
        centroidYPixels = getResult("Y", i) + y - padding;
        centroidX = centroidXPixels * micronsPerPixel; // Convert to calibrated units
        centroidY = centroidYPixels * micronsPerPixel;

        solidity = getResult("Solidity", i);
        circularity = getResult("Circ.", i);
        aspectRatio = getResult("AR", i);

        // Add to global detail arrays
        detailParticleIDs = Array.concat(detailParticleIDs, p + 1); // 1-based numbering
        detailConcaveIDs = Array.concat(detailConcaveIDs, i + 1);
        detailAreas = Array.concat(detailAreas, area);
        detailPerimeters = Array.concat(detailPerimeters, perimeter);
        detailCentroids = Array.concat(detailCentroids, centroidX);
        detailCentroids = Array.concat(detailCentroids, centroidY);
        detailSolidities = Array.concat(detailSolidities, solidity);
        detailCircularities = Array.concat(detailCircularities, circularity);
        detailAspectRatios = Array.concat(detailAspectRatios, aspectRatio);
        detailClassifications = Array.concat(detailClassifications, 0); // Will be classified later
    }
    
    selectImage(concaveRegionsID);
    close();
}

// Re-enable screen updates
setBatchMode(false);

print("Analysis complete!");
print("Found " + nParticles + " particles with " + totalConcaveAreas + " total concave areas");

// Verify calibration is working by showing sample measurements
if (detailAreas.length > 0) {
    print("Sample concave area measurements (first 3):");
    maxSamples = minOf(3, detailAreas.length);
    for (i = 0; i < maxSamples; i++) {
        print("  Concave area " + (i+1) + ": " + d2s(detailAreas[i], 3) + " " + unit + "²");
    }
}

// Ask user about classification method
Dialog.create("Classification and Export Options");
Dialog.addMessage("Analysis complete!");
Dialog.addMessage("Particles: " + nParticles + ", Total concave areas: " + totalConcaveAreas);
Dialog.addMessage("");
Dialog.addMessage("Choose classification method for concavity size binning:");
Dialog.addChoice("Classification method:", newArray("auto", "percentile", "manual"), classificationMethod);
Dialog.addMessage("Auto: Statistical analysis to find natural boundaries");
Dialog.addMessage("Percentile: 33.3% / 66.7% split (small/moderate/large)");
Dialog.addMessage("Manual: User-defined boundaries");
Dialog.addMessage("");
Dialog.addNumber("Manual small threshold (" + unit + "²):", manualSmallThreshold);
Dialog.addNumber("Manual moderate threshold (" + unit + "²):", manualModerateThreshold);
Dialog.addMessage("");
Dialog.addCheckbox("Export results to Excel", true);
Dialog.show();

// Get user choices
classificationMethod = Dialog.getChoice();
manualSmallThreshold = Dialog.getNumber();
manualModerateThreshold = Dialog.getNumber();
exportToExcelChoice = Dialog.getCheckbox();

// =============================================================================
// CONCAVITY CLASSIFICATION
// =============================================================================

if (detailAreas.length > 0) {
    print("Classifying concavities by size...");

    // Determine classification boundaries
    determineBoundaries();

    // Reset counters
    totalSmallConcavities = 0;
    totalModerateConcavities = 0;
    totalLargeConcavities = 0;

    // Classify each concavity
    for (i = 0; i < detailAreas.length; i++) {
        area = detailAreas[i];

        if (area <= smallThreshold) {
            detailClassifications[i] = 1; // Small (surface roughness)
            totalSmallConcavities++;
        } else if (area <= moderateThreshold) {
            detailClassifications[i] = 2; // Moderate (waviness)
            totalModerateConcavities++;
        } else {
            detailClassifications[i] = 3; // Large (satellites)
            totalLargeConcavities++;
        }
    }

    print("Classification complete:");
    print("  Small (surface roughness): " + totalSmallConcavities + " concavities");
    print("  Moderate (waviness): " + totalModerateConcavities + " concavities");
    print("  Large (satellites): " + totalLargeConcavities + " concavities");
    print("  Boundaries: Small ≤ " + d2s(smallThreshold, 3) + " " + unit + "² < Moderate ≤ " + d2s(moderateThreshold, 3) + " " + unit + "² < Large");
}

// =============================================================================
// RESULTS OUTPUT
// =============================================================================

// Create summary results table
createSummaryTable();

// Create detailed results table
createDetailTable();

if (exportToExcelChoice) {
    exportToExcel();
}

print("Concave area analysis completed successfully!");

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================



// Function to create summary results table
function createSummaryTable() {
    // Close existing table if open
    if (isOpen(summaryTableName)) {
        selectWindow(summaryTableName);
        run("Close");
    }

    // Create summary table
    Table.create(summaryTableName);
    selectWindow(summaryTableName);

    // Populate summary table
    particleNumbers = newArray(nParticles);
    for (i = 0; i < nParticles; i++) {
        particleNumbers[i] = i + 1; // 1-based numbering
    }

    Table.setColumn("Particle_ID", particleNumbers);
    Table.setColumn("Particle_Area_" + unit + "²", particleAreas);
    Table.setColumn("Particle_Perimeter_" + unit, particlePerimeters);
    Table.setColumn("Concave_Area_Count", concaveAreaCounts);

    // Calculate particle-level classification counts
    smallCounts = newArray(nParticles);
    moderateCounts = newArray(nParticles);
    largeCounts = newArray(nParticles);

    // Initialize counts to zero
    for (i = 0; i < nParticles; i++) {
        smallCounts[i] = 0;
        moderateCounts[i] = 0;
        largeCounts[i] = 0;
    }

    // Count classifications for each particle
    for (i = 0; i < detailParticleIDs.length; i++) {
        particleIndex = detailParticleIDs[i] - 1; // Convert to 0-based index
        if (particleIndex >= 0 && particleIndex < nParticles) {
            if (detailClassifications[i] == 1) {
                smallCounts[particleIndex]++;
            } else if (detailClassifications[i] == 2) {
                moderateCounts[particleIndex]++;
            } else if (detailClassifications[i] == 3) {
                largeCounts[particleIndex]++;
            }
        }
    }

    Table.setColumn("Small_Concavities", smallCounts);
    Table.setColumn("Moderate_Concavities", moderateCounts);
    Table.setColumn("Large_Concavities", largeCounts);

    // Calculate concave area density (concave areas per unit area)
    concaveDensities = newArray(nParticles);
    for (i = 0; i < nParticles; i++) {
        if (particleAreas[i] > 0) {
            concaveDensities[i] = concaveAreaCounts[i] / particleAreas[i];
        } else {
            concaveDensities[i] = 0;
        }
    }
    Table.setColumn("Concave_Density_per_" + unit + "²", concaveDensities);

    Table.update;
}

// Function to create detailed results table
function createDetailTable() {
    // Close existing table if open
    if (isOpen(detailTableName)) {
        selectWindow(detailTableName);
        run("Close");
    }

    // Only create if we have detailed results
    if (detailParticleIDs.length == 0) {
        print("No concave areas found - skipping detailed table");
        return;
    }

    // Create detailed table
    Table.create(detailTableName);
    selectWindow(detailTableName);

    // Populate detailed table
    Table.setColumn("Particle_ID", detailParticleIDs);
    Table.setColumn("Concave_Area_ID", detailConcaveIDs);
    Table.setColumn("Area_" + unit + "²", detailAreas);
    Table.setColumn("Perimeter_" + unit, detailPerimeters);

    // Extract X and Y coordinates from centroid pairs
    centroidX = newArray(detailParticleIDs.length);
    centroidY = newArray(detailParticleIDs.length);
    for (i = 0; i < detailParticleIDs.length; i++) {
        centroidX[i] = detailCentroids[i * 2];
        centroidY[i] = detailCentroids[i * 2 + 1];
    }

    Table.setColumn("Centroid_X_" + unit, centroidX);
    Table.setColumn("Centroid_Y_" + unit, centroidY);
    Table.setColumn("Solidity", detailSolidities);
    Table.setColumn("Circularity", detailCircularities);
    Table.setColumn("Aspect_Ratio", detailAspectRatios);

    // Add classification data
    classificationNames = newArray(detailParticleIDs.length);
    for (i = 0; i < detailParticleIDs.length; i++) {
        if (detailClassifications[i] == 1) {
            classificationNames[i] = "Small";
        } else if (detailClassifications[i] == 2) {
            classificationNames[i] = "Moderate";
        } else if (detailClassifications[i] == 3) {
            classificationNames[i] = "Large";
        } else {
            classificationNames[i] = "Unclassified";
        }
    }
    Table.setColumn("Classification", classificationNames);

    Table.update;
}

// Function to export results to Excel
function exportToExcel() {
    print("");
    print("=== EXPORTING TO EXCEL ===");

    // Get output directory
    outputDir = getDirectory("Choose directory for Excel export");
    if (outputDir == "") return;

    // Get base name from original image title (without extension)
    dotIndex = lastIndexOf(originalTitle, ".");
    if (dotIndex > 0) {
        baseName = substring(originalTitle, 0, dotIndex);
    } else {
        baseName = originalTitle;
    }

    // Create Excel file path
    excelFile = outputDir + baseName + "_ConcaveAreas.xlsx";

    // Export summary table (copy to Results table first)
    if (isOpen(summaryTableName)) {
        // Copy summary table to Results table for Excel export
        copyTableToResults(summaryTableName);

        // Export from Results table
        run("Read and Write Excel", "file=[" + excelFile + "] sheet=[Summary] no_count_column stack_results");
    }

    // Export detailed measurements organized by particle groups (10 particles per sheet)
    exportDetailedByParticleGroups(excelFile);

    print("Excel export completed: " + baseName + "_ConcaveAreas.xlsx");
    showMessage("Export completed!\nFile saved as: " + baseName + "_ConcaveAreas.xlsx");
}

// Function to export detailed measurements organized by particle groups
function exportDetailedByParticleGroups(excelFile) {
    if (detailParticleIDs.length == 0) {
        return;
    }

    particlesPerSheet = 10;
    totalSheets = ceil(nParticles / particlesPerSheet);

    for (sheet = 0; sheet < totalSheets; sheet++) {
        // Calculate particle range for this sheet
        startParticle = sheet * particlesPerSheet + 1; // 1-based
        endParticle = minOf((sheet + 1) * particlesPerSheet, nParticles);

        // Create worksheet name
        sheetName = "Particles_" + startParticle + "-" + endParticle;

        // Create temporary table for this sheet
        tempTableName = "TempExport_" + sheet;
        createParticleGroupTable(tempTableName, startParticle, endParticle);

        // Export to Excel worksheet if table has data
        if (isOpen(tempTableName)) {
            selectWindow(tempTableName);
            rowCount = Table.size;
            if (rowCount > 0) {
                // Copy table data to Results table for Excel export
                copyTableToResults(tempTableName);

                // Export from Results table
                run("Read and Write Excel", "file=[" + excelFile + "] sheet=[" + sheetName + "] no_count_column stack_results");
            }

            // Close temporary table
            selectWindow(tempTableName);
            run("Close");
        }
    }
}

// Function to create a table for a specific group of particles
function createParticleGroupTable(tableName, startParticle, endParticle) {
    // Create new table
    Table.create(tableName);
    selectWindow(tableName);

    // Prepare arrays for this particle group
    groupParticleIDs = newArray(0);
    groupConcaveIDs = newArray(0);
    groupAreas = newArray(0);
    groupPerimeters = newArray(0);
    groupCentroidsX = newArray(0);
    groupCentroidsY = newArray(0);
    groupSolidities = newArray(0);
    groupCircularities = newArray(0);
    groupAspectRatios = newArray(0);
    groupClassifications = newArray(0);

    // Find all concave areas belonging to particles in this range
    for (i = 0; i < detailParticleIDs.length; i++) {
        particleID = detailParticleIDs[i];

        // Check if this concave area belongs to a particle in our range
        if (particleID >= startParticle && particleID <= endParticle) {
            // Extract centroid coordinates
            centroidX = detailCentroids[i * 2];
            centroidY = detailCentroids[i * 2 + 1];

            // Add to group arrays
            groupParticleIDs = Array.concat(groupParticleIDs, particleID);
            groupConcaveIDs = Array.concat(groupConcaveIDs, detailConcaveIDs[i]);
            groupAreas = Array.concat(groupAreas, detailAreas[i]);
            groupPerimeters = Array.concat(groupPerimeters, detailPerimeters[i]);
            groupCentroidsX = Array.concat(groupCentroidsX, centroidX);
            groupCentroidsY = Array.concat(groupCentroidsY, centroidY);
            groupSolidities = Array.concat(groupSolidities, detailSolidities[i]);
            groupCircularities = Array.concat(groupCircularities, detailCircularities[i]);
            groupAspectRatios = Array.concat(groupAspectRatios, detailAspectRatios[i]);
            groupClassifications = Array.concat(groupClassifications, detailClassifications[i]);
        }
    }

    // Populate the table with all data at once
    if (groupParticleIDs.length > 0) {
        Table.setColumn("Particle_ID", groupParticleIDs);
        Table.setColumn("Concave_Area_ID", groupConcaveIDs);
        Table.setColumn("Area_" + unit + "²", groupAreas);
        Table.setColumn("Perimeter_" + unit, groupPerimeters);
        Table.setColumn("Centroid_X_" + unit, groupCentroidsX);
        Table.setColumn("Centroid_Y_" + unit, groupCentroidsY);
        Table.setColumn("Solidity", groupSolidities);
        Table.setColumn("Circularity", groupCircularities);
        Table.setColumn("Aspect_Ratio", groupAspectRatios);

        // Add classification names
        groupClassificationNames = newArray(groupParticleIDs.length);
        for (i = 0; i < groupParticleIDs.length; i++) {
            if (groupClassifications[i] == 1) {
                groupClassificationNames[i] = "Small";
            } else if (groupClassifications[i] == 2) {
                groupClassificationNames[i] = "Moderate";
            } else if (groupClassifications[i] == 3) {
                groupClassificationNames[i] = "Large";
            } else {
                groupClassificationNames[i] = "Unclassified";
            }
        }
        Table.setColumn("Classification", groupClassificationNames);

        Table.update;
    }
}

// Helper function to get minimum of two values
function minOf(a, b) {
    if (a < b) {
        return a;
    } else {
        return b;
    }
}

// Helper function to get ceiling of division
function ceil(value) {
    intValue = floor(value);
    if (value > intValue) {
        return intValue + 1;
    } else {
        return intValue;
    }
}



// Function to determine classification boundaries
function determineBoundaries() {
    if (classificationMethod == "manual") {
        // Use manually specified boundaries
        smallThreshold = manualSmallThreshold;
        moderateThreshold = manualModerateThreshold;
        print("Using manual boundaries: " + d2s(smallThreshold, 3) + " and " + d2s(moderateThreshold, 3) + " " + unit + "²");

    } else if (classificationMethod == "percentile") {
        // Use percentile-based boundaries
        calculatePercentileBoundaries();
        print("Using percentile boundaries (" + smallPercentile + "%, " + moderatePercentile + "%): " +
              d2s(smallThreshold, 3) + " and " + d2s(moderateThreshold, 3) + " " + unit + "²");

    } else {
        // Auto-determine boundaries using statistical analysis
        calculateAutoBoundaries();
        print("Using auto-determined boundaries: " + d2s(smallThreshold, 3) + " and " + d2s(moderateThreshold, 3) + " " + unit + "²");
    }
}

// Function to calculate percentile-based boundaries
function calculatePercentileBoundaries() {
    // Sort areas to find percentiles
    sortedAreas = Array.copy(detailAreas);
    Array.sort(sortedAreas);

    n = sortedAreas.length;
    smallIndex = floor(n * smallPercentile / 100);
    moderateIndex = floor(n * moderatePercentile / 100);

    // Ensure indices are within bounds
    if (smallIndex >= n) smallIndex = n - 1;
    if (moderateIndex >= n) moderateIndex = n - 1;
    if (smallIndex < 0) smallIndex = 0;
    if (moderateIndex <= smallIndex) moderateIndex = smallIndex + 1;

    smallThreshold = sortedAreas[smallIndex];
    moderateThreshold = sortedAreas[moderateIndex];
}

// Function to calculate auto-determined boundaries using statistical analysis
function calculateAutoBoundaries() {
    // Sort areas for analysis
    sortedAreas = Array.copy(detailAreas);
    Array.sort(sortedAreas);

    n = sortedAreas.length;
    if (n < 3) {
        // Not enough data for classification
        smallThreshold = sortedAreas[0];
        moderateThreshold = sortedAreas[n-1];
        return;
    }

    // Calculate basic statistics
    mean = calculateMean(detailAreas);
    stdDev = calculateStdDev(detailAreas, mean);
    median = getPercentile(sortedAreas, 50);

    // Method 1: Robust percentile-based approach (most reliable)
    // Use 30th and 70th percentiles as natural breakpoints
    p30 = getPercentile(sortedAreas, 30);
    p70 = getPercentile(sortedAreas, 70);

    // Method 2: Standard deviation based (for normally distributed data)
    // Small: below median - 0.5*stdDev, Large: above median + 0.5*stdDev
    sdSmall = median - 0.5 * stdDev;
    sdLarge = median + 0.5 * stdDev;

    // Method 3: Quartile-based approach
    q1 = getPercentile(sortedAreas, 25);
    q3 = getPercentile(sortedAreas, 75);

    // Choose the most appropriate method based on data distribution
    // Check if data is heavily skewed
    skewness = (mean - median) / stdDev;

    if (abs(skewness) < 0.5) {
        // Data is roughly normal - use standard deviation method
        smallThreshold = maxOf(sdSmall, sortedAreas[0]);
        moderateThreshold = minOf(sdLarge, sortedAreas[n-1]);
    } else {
        // Data is skewed - use robust percentile method
        smallThreshold = p30;
        moderateThreshold = p70;
    }

    // Ensure boundaries are reasonable and create meaningful groups
    if (smallThreshold <= sortedAreas[0]) {
        smallThreshold = getPercentile(sortedAreas, 25);
    }
    if (moderateThreshold >= sortedAreas[n-1]) {
        moderateThreshold = getPercentile(sortedAreas, 75);
    }
    if (smallThreshold >= moderateThreshold) {
        // Fallback to simple tertile split
        smallThreshold = getPercentile(sortedAreas, 33.3);
        moderateThreshold = getPercentile(sortedAreas, 66.7);
    }

    // Final validation - ensure we have at least some data in each category
    smallCount = 0;
    moderateCount = 0;
    largeCount = 0;

    for (i = 0; i < n; i++) {
        if (sortedAreas[i] <= smallThreshold) smallCount++;
        else if (sortedAreas[i] <= moderateThreshold) moderateCount++;
        else largeCount++;
    }

    // If any category is empty, adjust boundaries
    if (smallCount == 0 || moderateCount == 0 || largeCount == 0) {
        smallThreshold = getPercentile(sortedAreas, 33.3);
        moderateThreshold = getPercentile(sortedAreas, 66.7);
    }
}

// Function to copy Table data to Results table for Excel export
function copyTableToResults(tableName) {
    // Clear Results table
    run("Clear Results");

    // Select the source table
    selectWindow(tableName);

    // Get table dimensions
    rowCount = Table.size;
    columnNames = Table.headings;
    columnArray = split(columnNames, "\t");

    // Copy data row by row
    for (row = 0; row < rowCount; row++) {
        for (col = 0; col < columnArray.length; col++) {
            columnName = columnArray[col];
            value = Table.get(columnName, row);
            setResult(columnName, row, value);
        }
    }

    updateResults();
}

// Statistical helper functions
function calculateMean(array) {
    sum = 0;
    for (i = 0; i < array.length; i++) {
        sum += array[i];
    }
    return sum / array.length;
}

function calculateStdDev(array, mean) {
    sumSquares = 0;
    for (i = 0; i < array.length; i++) {
        diff = array[i] - mean;
        sumSquares += diff * diff;
    }
    return sqrt(sumSquares / (array.length - 1));
}

function getPercentile(sortedArray, percentile) {
    n = sortedArray.length;
    index = (percentile / 100) * (n - 1);

    if (index == floor(index)) {
        return sortedArray[index];
    } else {
        lower = floor(index);
        upper = ceil(index);
        if (upper >= n) upper = n - 1;
        weight = index - lower;
        return sortedArray[lower] * (1 - weight) + sortedArray[upper] * weight;
    }
}

function findNaturalBreaks(sortedArray) {
    // Simplified natural breaks algorithm
    // Find the two points that maximize between-group variance
    n = sortedArray.length;
    bestBreaks = newArray(2);
    bestVariance = 0;

    // Try different break points
    for (i = floor(n * 0.2); i < floor(n * 0.4); i++) {
        for (j = floor(n * 0.6); j < floor(n * 0.8); j++) {
            variance = calculateBetweenGroupVariance(sortedArray, i, j);
            if (variance > bestVariance) {
                bestVariance = variance;
                bestBreaks[0] = sortedArray[i];
                bestBreaks[1] = sortedArray[j];
            }
        }
    }

    return bestBreaks;
}

function calculateBetweenGroupVariance(sortedArray, break1, break2) {
    // Calculate variance between three groups
    n = sortedArray.length;

    // Group means
    mean1 = 0; count1 = 0;
    mean2 = 0; count2 = 0;
    mean3 = 0; count3 = 0;

    for (i = 0; i < break1; i++) {
        mean1 += sortedArray[i];
        count1++;
    }
    if (count1 > 0) mean1 /= count1;

    for (i = break1; i < break2; i++) {
        mean2 += sortedArray[i];
        count2++;
    }
    if (count2 > 0) mean2 /= count2;

    for (i = break2; i < n; i++) {
        mean3 += sortedArray[i];
        count3++;
    }
    if (count3 > 0) mean3 /= count3;

    // Overall mean
    overallMean = calculateMean(sortedArray);

    // Between-group variance
    variance = count1 * (mean1 - overallMean) * (mean1 - overallMean) +
               count2 * (mean2 - overallMean) * (mean2 - overallMean) +
               count3 * (mean3 - overallMean) * (mean3 - overallMean);

    return variance;
}

function maxOf(a, b) {
    if (a > b) {
        return a;
    } else {
        return b;
    }
}

function abs(value) {
    if (value < 0) {
        return -value;
    } else {
        return value;
    }
}


