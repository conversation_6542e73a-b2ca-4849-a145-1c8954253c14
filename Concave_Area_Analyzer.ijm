// Concave Area Analyzer - ImageJ Macro
// Analyzes particles and counts distinct concave areas with their respective measurements
// Optimized for speed and comprehensive concavity analysis

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
minConcaveAreaPixels = 5;       // Minimum concave area to count (pixels)
maxParticles = 1000;            // Maximum particles to analyze
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// Results table names
summaryTableName = "Concave_Areas_Summary";
detailTableName = "Concave_Areas_Detail";

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;
pixelsPerMicron = pixelDistance / realDistance;

print("\\Clear");
print("=== CONCAVE AREA ANALYZER ===");
print("Image: " + originalTitle);
print("Calibration: " + d2s(micronsPerPixel, 4) + " " + unit + "/pixel");
print("Starting analysis...");

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// Set calibration
run("Set Scale...", "distance=" + 1 + " known=" + 1 + " unit= pixel");

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area perimeter centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

if (nParticles > maxParticles) {
    exit("Too many particles (" + nParticles + "). Maximum allowed: " + maxParticles);
}

print("Found " + nParticles + " particles for analysis");

// Store basic particle measurements
particleAreas = newArray(nParticles);
particlePerimeters = newArray(nParticles);
particleCentroids = newArray(nParticles * 2); // x,y pairs

for (i = 0; i < nParticles; i++) {
    particleAreas[i] = getResult("Area", i);
    particlePerimeters[i] = getResult("Perim.", i);
    particleCentroids[i * 2] = getResult("X", i);
    particleCentroids[i * 2 + 1] = getResult("Y", i);
}

// =============================================================================
// CONCAVE AREA ANALYSIS
// =============================================================================

// Arrays to store results
concaveAreaCounts = newArray(nParticles);
totalConcaveAreas = 0;

// Detailed results arrays (will be resized as needed)
detailParticleIDs = newArray(0);
detailConcaveIDs = newArray(0);
detailAreas = newArray(0);
detailPerimeters = newArray(0);
detailCentroids = newArray(0); // Will store x,y pairs
detailSolidities = newArray(0);
detailCircularities = newArray(0);
detailAspectRatios = newArray(0);

print("Analyzing concave areas...");

// Enable batch mode for speed
setBatchMode(true);

// Main analysis loop
for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);
    
    // Get particle ROI bounds for creating minimal working image
    roiManager("select", p);
    Roi.getBounds(x, y, width, height);
    
    // Create minimal working image for this particle (with padding)
    padding = 10;
    newImage("ParticleWork", "8-bit black", width + 2*padding, height + 2*padding, 1);
    particleWorkID = getImageID();
    
    // Copy particle to smaller image
    selectImage(workingID);
    roiManager("select", p);
    run("Copy");
    selectImage(particleWorkID);
    makeRectangle(padding, padding, width, height);
    run("Paste");
    run("Select None");
    
    // Create convex hull version
    selectImage(particleWorkID);
    run("Duplicate...", "title=ConvexHull");
    convexHullID = getImageID();
    
    // Find particle and create convex hull
    setThreshold(128, 255);
    run("Create Selection");
    if (selectionType() != -1) {
        run("Convex Hull");
        run("Clear", "slice");
        setForegroundColor(255, 255, 255);
        run("Fill", "slice");
    }
    run("Select None");
    resetThreshold();
    
    // Calculate concave regions (convex hull - original particle)
    imageCalculator("Subtract create", convexHullID, particleWorkID);
    concaveRegionsID = getImageID();
    
    // Clean up intermediate images
    selectImage(convexHullID);
    close();
    selectImage(particleWorkID);
    close();
    
    // Analyze individual concave areas using connected components
    selectImage(concaveRegionsID);
    nConcaveAreas = analyzeConcaveRegions(p, padding, x, y);
    concaveAreaCounts[p] = nConcaveAreas;
    totalConcaveAreas += nConcaveAreas;
    
    selectImage(concaveRegionsID);
    close();
}

// Re-enable screen updates
setBatchMode(false);

print("Analysis complete!");
print("Total concave areas found: " + totalConcaveAreas);

// =============================================================================
// RESULTS OUTPUT
// =============================================================================

// Create summary results table
createSummaryTable();

// Create detailed results table
createDetailTable();

// Display results
print("");
print("=== RESULTS SUMMARY ===");
print("Particles analyzed: " + nParticles);
print("Total concave areas: " + totalConcaveAreas);
print("Average concave areas per particle: " + d2s(totalConcaveAreas / nParticles, 2));

// Ask user about Excel export
Dialog.create("Export Results");
Dialog.addMessage("Analysis complete!");
Dialog.addMessage("Particles: " + nParticles + ", Total concave areas: " + totalConcaveAreas);
Dialog.addCheckbox("Export results to Excel", true);
Dialog.addCheckbox("Create visual overlay", true);
Dialog.show();

if (Dialog.getCheckbox()) {
    exportToExcel();
}

if (Dialog.getCheckbox()) {
    createVisualOverlay();
}

print("Concave area analysis completed successfully!");

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

// Function to analyze concave regions in a single particle
function analyzeConcaveRegions(particleIndex, padding, offsetX, offsetY) {
    // Use "Analyze Particles" to find connected components in concave regions
    run("Set Measurements...", "area perimeter centroid bounding shape fit redirect=None decimal=3");
    run("Analyze Particles...", "size=" + minConcaveAreaPixels + "-Infinity display clear");

    nConcaveAreas = nResults;

    // Directly populate the global detail arrays
    for (i = 0; i < nConcaveAreas; i++) {
        // Get measurements (already in calibrated units)
        area = getResult("Area", i);
        perimeter = getResult("Perim.", i);
        centroidX = getResult("X", i) + offsetX - padding; // Adjust for padding and offset
        centroidY = getResult("Y", i) + offsetY - padding;
        solidity = getResult("Solidity", i);
        circularity = getResult("Circ.", i);
        aspectRatio = getResult("AR", i);

        // Add to global detail arrays
        detailParticleIDs = Array.concat(detailParticleIDs, particleIndex + 1); // 1-based numbering
        detailConcaveIDs = Array.concat(detailConcaveIDs, i + 1);
        detailAreas = Array.concat(detailAreas, area);
        detailPerimeters = Array.concat(detailPerimeters, perimeter);
        detailCentroids = Array.concat(detailCentroids, centroidX);
        detailCentroids = Array.concat(detailCentroids, centroidY);
        detailSolidities = Array.concat(detailSolidities, solidity);
        detailCircularities = Array.concat(detailCircularities, circularity);
        detailAspectRatios = Array.concat(detailAspectRatios, aspectRatio);
    }

    // Return the number of concave areas found
    return nConcaveAreas;
}

// Function to create summary results table
function createSummaryTable() {
    // Close existing table if open
    if (isOpen(summaryTableName)) {
        selectWindow(summaryTableName);
        run("Close");
    }

    // Create summary table
    Table.create(summaryTableName);
    selectWindow(summaryTableName);

    // Populate summary table
    particleNumbers = newArray(nParticles);
    for (i = 0; i < nParticles; i++) {
        particleNumbers[i] = i + 1; // 1-based numbering
    }

    Table.setColumn("Particle_ID", particleNumbers);
    Table.setColumn("Particle_Area_" + unit + "²", particleAreas);
    Table.setColumn("Particle_Perimeter_" + unit, particlePerimeters);
    Table.setColumn("Concave_Area_Count", concaveAreaCounts);

    // Calculate concave area density (concave areas per unit area)
    concaveDensities = newArray(nParticles);
    for (i = 0; i < nParticles; i++) {
        concaveDensities[i] = (particleAreas[i] > 0) ? concaveAreaCounts[i] / particleAreas[i] : 0;
    }
    Table.setColumn("Concave_Density_per_" + unit + "²", concaveDensities);

    Table.update;
    print("Summary table created: " + summaryTableName);
}

// Function to create detailed results table
function createDetailTable() {
    // Close existing table if open
    if (isOpen(detailTableName)) {
        selectWindow(detailTableName);
        run("Close");
    }

    // Only create if we have detailed results
    if (detailParticleIDs.length == 0) {
        print("No concave areas found - skipping detailed table");
        return;
    }

    // Create detailed table
    Table.create(detailTableName);
    selectWindow(detailTableName);

    // Populate detailed table
    Table.setColumn("Particle_ID", detailParticleIDs);
    Table.setColumn("Concave_Area_ID", detailConcaveIDs);
    Table.setColumn("Area_" + unit + "²", detailAreas);
    Table.setColumn("Perimeter_" + unit, detailPerimeters);

    // Extract X and Y coordinates from centroid pairs
    centroidX = newArray(detailParticleIDs.length);
    centroidY = newArray(detailParticleIDs.length);
    for (i = 0; i < detailParticleIDs.length; i++) {
        centroidX[i] = detailCentroids[i * 2];
        centroidY[i] = detailCentroids[i * 2 + 1];
    }

    Table.setColumn("Centroid_X_" + unit, centroidX);
    Table.setColumn("Centroid_Y_" + unit, centroidY);
    Table.setColumn("Solidity", detailSolidities);
    Table.setColumn("Circularity", detailCircularities);
    Table.setColumn("Aspect_Ratio", detailAspectRatios);

    Table.update;
    print("Detailed table created: " + detailTableName);
}

// Function to export results to Excel
function exportToExcel() {
    print("");
    print("=== EXPORTING TO EXCEL ===");

    // Get output directory
    outputDir = getDirectory("Choose directory for Excel export");
    if (outputDir == "") return;

    // Get base name from original image title (without extension)
    dotIndex = lastIndexOf(originalTitle, ".");
    if (dotIndex > 0) {
        baseName = substring(originalTitle, 0, dotIndex);
    } else {
        baseName = originalTitle;
    }

    // Create Excel file path
    excelFile = outputDir + baseName + "_ConcaveAreas.xlsx";

    // Export summary table
    if (isOpen(summaryTableName)) {
        selectWindow(summaryTableName);
        run("Read and Write Excel", "file=[" + excelFile + "] sheet=[Summary] no_count_column stack_results");
        print("Exported summary to worksheet: Summary");
    }

    // Export detailed table
    if (isOpen(detailTableName)) {
        selectWindow(detailTableName);
        run("Read and Write Excel", "file=[" + excelFile + "] sheet=[Detailed_Measurements] no_count_column stack_results");
        print("Exported detailed measurements to worksheet: Detailed_Measurements");
    }

    print("Export completed!");
    print("Excel file saved as: " + baseName + "_ConcaveAreas.xlsx");
    showMessage("Export completed!\nFile saved as: " + baseName + "_ConcaveAreas.xlsx");
}

// Function to create visual overlay showing concave areas
function createVisualOverlay() {
    print("");
    print("=== CREATING VISUAL OVERLAY ===");

    // Select working image for overlay
    selectImage(workingID);

    // Remove existing overlay
    run("Remove Overlay");

    // Create overlay showing particles and their concave areas
    concaveIndex = 0;

    for (p = 0; p < nParticles; p++) {
        // Show particle outline
        roiManager("select", p);
        roiManager("Set Color", "yellow");
        roiManager("Set Line Width", 2);
        run("Add Selection...");

        // Add particle number label
        Roi.getBounds(x, y, width, height);
        centerX = x + width/2;
        centerY = y + height/2;

        setColor("white");
        setFont("SansSerif", 12, "bold");
        makeText("P" + (p+1), centerX-10, centerY-15);
        run("Add Selection...");

        // Add concave area count label
        setColor("cyan");
        makeText("C:" + concaveAreaCounts[p], centerX-10, centerY+5);
        run("Add Selection...");

        // Mark concave area centroids
        for (c = 0; c < concaveAreaCounts[p]; c++) {
            if (concaveIndex < detailCentroids.length / 2) {
                concaveX = detailCentroids[concaveIndex * 2];
                concaveY = detailCentroids[concaveIndex * 2 + 1];

                // Create small circle at concave area centroid
                makeOval(concaveX-3, concaveY-3, 6, 6);
                setColor("red");
                run("Add Selection...");

                // Add concave area number
                setColor("white");
                setFont("SansSerif", 8, "bold");
                makeText("" + (c+1), concaveX-2, concaveY-2);
                run("Add Selection...");

                concaveIndex++;
            }
        }
    }

    run("Select None");
    print("Visual overlay created showing particles (yellow) and concave areas (red dots)");
    print("P# = Particle number, C:# = Concave area count");
}
