// Concave Area Analyzer - ImageJ Macro
// Analyzes particles and counts distinct concave areas with their respective measurements
// Optimized for speed and comprehensive concavity analysis

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
minConcaveAreaPixels = 5;       // Minimum concave area to count (pixels)
maxParticles = 1000;            // Maximum particles to analyze
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// Results table names
summaryTableName = "Concave_Areas_Summary";
detailTableName = "Concave_Areas_Detail";

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;
pixelsPerMicron = pixelDistance / realDistance;

print("\\Clear");
print("=== CONCAVE AREA ANALYZER ===");
print("Image: " + originalTitle);
print("Calibration: " + d2s(micronsPerPixel, 4) + " " + unit + "/pixel");
print("Starting analysis...");

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// Set calibration
run("Set Scale...", "distance=" + 1 + " known=" + 1 + " unit= pixel");

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area perimeter centroid bounding redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

if (nParticles > maxParticles) {
    exit("Too many particles (" + nParticles + "). Maximum allowed: " + maxParticles);
}

print("Found " + nParticles + " particles for analysis");

// Store basic particle measurements
particleAreas = newArray(nParticles);
particlePerimeters = newArray(nParticles);
particleCentroids = newArray(nParticles * 2); // x,y pairs

for (i = 0; i < nParticles; i++) {
    particleAreas[i] = getResult("Area", i);
    particlePerimeters[i] = getResult("Perim.", i);
    particleCentroids[i * 2] = getResult("X", i);
    particleCentroids[i * 2 + 1] = getResult("Y", i);
}

// =============================================================================
// CONCAVE AREA ANALYSIS
// =============================================================================

// Arrays to store results
concaveAreaCounts = newArray(nParticles);
totalConcaveAreas = 0;

// Detailed results arrays (will be resized as needed)
detailParticleIDs = newArray(0);
detailConcaveIDs = newArray(0);
detailAreas = newArray(0);
detailPerimeters = newArray(0);
detailCentroids = newArray(0); // Will store x,y pairs
detailSolidities = newArray(0);
detailCircularities = newArray(0);
detailAspectRatios = newArray(0);

print("Analyzing concave areas...");

// Enable batch mode for speed
setBatchMode(true);

// Main analysis loop
for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);
    
    // Get particle ROI bounds for creating minimal working image
    roiManager("select", p);
    Roi.getBounds(x, y, width, height);
    
    // Create minimal working image for this particle (with padding)
    padding = 10;
    newImage("ParticleWork", "8-bit black", width + 2*padding, height + 2*padding, 1);
    particleWorkID = getImageID();
    
    // Copy particle to smaller image
    selectImage(workingID);
    roiManager("select", p);
    run("Copy");
    selectImage(particleWorkID);
    makeRectangle(padding, padding, width, height);
    run("Paste");
    run("Select None");
    
    // Create convex hull version
    selectImage(particleWorkID);
    run("Duplicate...", "title=ConvexHull");
    convexHullID = getImageID();
    
    // Find particle and create convex hull
    setThreshold(128, 255);
    run("Create Selection");
    if (selectionType() != -1) {
        run("Convex Hull");
        run("Clear", "slice");
        setForegroundColor(255, 255, 255);
        run("Fill", "slice");
    }
    run("Select None");
    resetThreshold();
    
    // Calculate concave regions (convex hull - original particle)
    imageCalculator("Subtract create", convexHullID, particleWorkID);
    concaveRegionsID = getImageID();
    
    // Clean up intermediate images
    selectImage(convexHullID);
    close();
    selectImage(particleWorkID);
    close();
    
    // Analyze individual concave areas using connected components
    selectImage(concaveRegionsID);
    nConcaveAreas = analyzeConcaveRegions(p, padding, x, y);
    concaveAreaCounts[p] = nConcaveAreas;
    totalConcaveAreas += nConcaveAreas;
    
    selectImage(concaveRegionsID);
    close();
}

// Re-enable screen updates
setBatchMode(false);

print("Analysis complete!");
print("Total concave areas found: " + totalConcaveAreas);
print("Detail arrays length: " + detailParticleIDs.length);

// Debug: Print first few entries
if (detailParticleIDs.length > 0) {
    print("First few detail entries:");
    maxDebug = minOf(10, detailParticleIDs.length);
    for (i = 0; i < maxDebug; i++) {
        print("  Entry " + (i+1) + ": Particle=" + detailParticleIDs[i] + ", Area=" + d2s(detailAreas[i], 3));
    }
}

// =============================================================================
// RESULTS OUTPUT
// =============================================================================

// Create summary results table
createSummaryTable();

// Create detailed results table
createDetailTable();

// Display results
print("");
print("=== RESULTS SUMMARY ===");
print("Particles analyzed: " + nParticles);
print("Total concave areas: " + totalConcaveAreas);
print("Average concave areas per particle: " + d2s(totalConcaveAreas / nParticles, 2));

// Ask user about Excel export
Dialog.create("Export Results");
Dialog.addMessage("Analysis complete!");
Dialog.addMessage("Particles: " + nParticles + ", Total concave areas: " + totalConcaveAreas);
Dialog.addCheckbox("Export results to Excel", true);
Dialog.addCheckbox("Create visual overlay", true);
Dialog.show();

if (Dialog.getCheckbox()) {
    exportToExcel();
}

if (Dialog.getCheckbox()) {
    createVisualOverlay();
}

print("Concave area analysis completed successfully!");

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

// Function to analyze concave regions in a single particle
function analyzeConcaveRegions(particleIndex, padding, offsetX, offsetY) {
    // Declare global variables to access them from within the function
    global detailParticleIDs, detailConcaveIDs, detailAreas, detailPerimeters;
    global detailCentroids, detailSolidities, detailCircularities, detailAspectRatios;
    global minConcaveAreaPixels;

    // Use "Analyze Particles" to find connected components in concave regions
    // IMPORTANT: Remove "clear" to avoid clearing previous results!
    run("Set Measurements...", "area perimeter centroid bounding shape fit redirect=None decimal=3");
    run("Analyze Particles...", "size=" + minConcaveAreaPixels + "-Infinity display");

    nConcaveAreas = nResults;

    // Debug output
    if (nConcaveAreas > 0) {
        print("Particle " + (particleIndex + 1) + ": Found " + nConcaveAreas + " concave areas");
    }

    // Directly populate the global detail arrays
    for (i = 0; i < nConcaveAreas; i++) {
        // Get measurements (already in calibrated units)
        area = getResult("Area", i);
        perimeter = getResult("Perim.", i);
        centroidX = getResult("X", i) + offsetX - padding; // Adjust for padding and offset
        centroidY = getResult("Y", i) + offsetY - padding;
        solidity = getResult("Solidity", i);
        circularity = getResult("Circ.", i);
        aspectRatio = getResult("AR", i);

        // Add to global detail arrays
        detailParticleIDs = Array.concat(detailParticleIDs, particleIndex + 1); // 1-based numbering
        detailConcaveIDs = Array.concat(detailConcaveIDs, i + 1);
        detailAreas = Array.concat(detailAreas, area);
        detailPerimeters = Array.concat(detailPerimeters, perimeter);
        detailCentroids = Array.concat(detailCentroids, centroidX);
        detailCentroids = Array.concat(detailCentroids, centroidY);
        detailSolidities = Array.concat(detailSolidities, solidity);
        detailCircularities = Array.concat(detailCircularities, circularity);
        detailAspectRatios = Array.concat(detailAspectRatios, aspectRatio);

        // Debug output for first few entries
        if (detailParticleIDs.length <= 10) {
            print("  Concave area " + (i + 1) + ": Area=" + d2s(area, 3) + ", Particle=" + (particleIndex + 1));
        }
    }

    // Debug: Check array length after processing this particle
    print("  After particle " + (particleIndex + 1) + ": detailParticleIDs.length = " + detailParticleIDs.length);

    // Clear results for next particle to avoid accumulation in Results table
    run("Clear Results");

    // Return the number of concave areas found
    return nConcaveAreas;
}

// Function to create summary results table
function createSummaryTable() {
    // Close existing table if open
    if (isOpen(summaryTableName)) {
        selectWindow(summaryTableName);
        run("Close");
    }

    // Create summary table
    Table.create(summaryTableName);
    selectWindow(summaryTableName);

    // Populate summary table
    particleNumbers = newArray(nParticles);
    for (i = 0; i < nParticles; i++) {
        particleNumbers[i] = i + 1; // 1-based numbering
    }

    Table.setColumn("Particle_ID", particleNumbers);
    Table.setColumn("Particle_Area_" + unit + "²", particleAreas);
    Table.setColumn("Particle_Perimeter_" + unit, particlePerimeters);
    Table.setColumn("Concave_Area_Count", concaveAreaCounts);

    // Calculate concave area density (concave areas per unit area)
    concaveDensities = newArray(nParticles);
    for (i = 0; i < nParticles; i++) {
        if (particleAreas[i] > 0) {
            concaveDensities[i] = concaveAreaCounts[i] / particleAreas[i];
        } else {
            concaveDensities[i] = 0;
        }
    }
    Table.setColumn("Concave_Density_per_" + unit + "²", concaveDensities);

    Table.update;
    print("Summary table created: " + summaryTableName);
}

// Function to create detailed results table
function createDetailTable() {
    // Declare global variables
    global detailParticleIDs, detailConcaveIDs, detailAreas, detailPerimeters;
    global detailCentroids, detailSolidities, detailCircularities, detailAspectRatios;
    global detailTableName, unit;

    // Close existing table if open
    if (isOpen(detailTableName)) {
        selectWindow(detailTableName);
        run("Close");
    }

    // Only create if we have detailed results
    if (detailParticleIDs.length == 0) {
        print("No concave areas found - skipping detailed table");
        return;
    }

    // Create detailed table
    Table.create(detailTableName);
    selectWindow(detailTableName);

    // Populate detailed table
    Table.setColumn("Particle_ID", detailParticleIDs);
    Table.setColumn("Concave_Area_ID", detailConcaveIDs);
    Table.setColumn("Area_" + unit + "²", detailAreas);
    Table.setColumn("Perimeter_" + unit, detailPerimeters);

    // Extract X and Y coordinates from centroid pairs
    centroidX = newArray(detailParticleIDs.length);
    centroidY = newArray(detailParticleIDs.length);
    for (i = 0; i < detailParticleIDs.length; i++) {
        centroidX[i] = detailCentroids[i * 2];
        centroidY[i] = detailCentroids[i * 2 + 1];
    }

    Table.setColumn("Centroid_X_" + unit, centroidX);
    Table.setColumn("Centroid_Y_" + unit, centroidY);
    Table.setColumn("Solidity", detailSolidities);
    Table.setColumn("Circularity", detailCircularities);
    Table.setColumn("Aspect_Ratio", detailAspectRatios);

    Table.update;
    print("Detailed table created: " + detailTableName);
}

// Function to export results to Excel
function exportToExcel() {
    print("");
    print("=== EXPORTING TO EXCEL ===");

    // Get output directory
    outputDir = getDirectory("Choose directory for Excel export");
    if (outputDir == "") return;

    // Get base name from original image title (without extension)
    dotIndex = lastIndexOf(originalTitle, ".");
    if (dotIndex > 0) {
        baseName = substring(originalTitle, 0, dotIndex);
    } else {
        baseName = originalTitle;
    }

    // Create Excel file path
    excelFile = outputDir + baseName + "_ConcaveAreas.xlsx";

    // Export summary table
    if (isOpen(summaryTableName)) {
        selectWindow(summaryTableName);
        run("Read and Write Excel", "file=[" + excelFile + "] sheet=[Summary] no_count_column stack_results");
        print("Exported summary to worksheet: Summary");
    }

    // Export detailed measurements organized by particle groups (10 particles per sheet)
    exportDetailedByParticleGroups(excelFile);

    print("Export completed!");
    print("Excel file saved as: " + baseName + "_ConcaveAreas.xlsx");
    showMessage("Export completed!\nFile saved as: " + baseName + "_ConcaveAreas.xlsx");
}

// Function to export detailed measurements organized by particle groups
function exportDetailedByParticleGroups(excelFile) {
    // Declare global variables
    global detailParticleIDs, detailAreas, nParticles;

    if (detailParticleIDs.length == 0) {
        print("No concave areas found - skipping detailed export");
        return;
    }

    print("DEBUG: Starting export with " + detailParticleIDs.length + " total concave areas");
    print("DEBUG: Detail arrays - ParticleIDs: " + detailParticleIDs.length + ", Areas: " + detailAreas.length);

    particlesPerSheet = 10;
    totalSheets = ceil(nParticles / particlesPerSheet);

    print("Creating " + totalSheets + " detailed worksheets (" + particlesPerSheet + " particles per sheet)");

    for (sheet = 0; sheet < totalSheets; sheet++) {
        // Calculate particle range for this sheet
        startParticle = sheet * particlesPerSheet + 1; // 1-based
        endParticle = minOf((sheet + 1) * particlesPerSheet, nParticles);

        // Create worksheet name
        sheetName = "Particles_" + startParticle + "-" + endParticle;

        // Create temporary table for this sheet
        tempTableName = "TempExport_" + sheet;
        createParticleGroupTable(tempTableName, startParticle, endParticle);

        // Export to Excel worksheet if table has data
        if (isOpen(tempTableName)) {
            selectWindow(tempTableName);
            rowCount = Table.size;
            if (rowCount > 0) {
                run("Read and Write Excel", "file=[" + excelFile + "] sheet=[" + sheetName + "] no_count_column stack_results");
                print("Exported " + rowCount + " concave areas to worksheet: " + sheetName);
            } else {
                print("No concave areas found for particles " + startParticle + "-" + endParticle);
            }

            // Close temporary table
            selectWindow(tempTableName);
            run("Close");
        }
    }
}

// Function to create a table for a specific group of particles
function createParticleGroupTable(tableName, startParticle, endParticle) {
    // Declare global variables
    global detailParticleIDs, detailConcaveIDs, detailAreas, detailPerimeters;
    global detailCentroids, detailSolidities, detailCircularities, detailAspectRatios;
    global unit;

    // Create new table
    Table.create(tableName);
    selectWindow(tableName);

    // Prepare arrays for this particle group
    groupParticleIDs = newArray(0);
    groupConcaveIDs = newArray(0);
    groupAreas = newArray(0);
    groupPerimeters = newArray(0);
    groupCentroidsX = newArray(0);
    groupCentroidsY = newArray(0);
    groupSolidities = newArray(0);
    groupCircularities = newArray(0);
    groupAspectRatios = newArray(0);

    // Find all concave areas belonging to particles in this range
    print("DEBUG: Searching for particles " + startParticle + "-" + endParticle + " in " + detailParticleIDs.length + " total entries");

    for (i = 0; i < detailParticleIDs.length; i++) {
        particleID = detailParticleIDs[i];

        // Check if this concave area belongs to a particle in our range
        if (particleID >= startParticle && particleID <= endParticle) {
            // Extract centroid coordinates
            centroidX = detailCentroids[i * 2];
            centroidY = detailCentroids[i * 2 + 1];

            // Add to group arrays
            groupParticleIDs = Array.concat(groupParticleIDs, particleID);
            groupConcaveIDs = Array.concat(groupConcaveIDs, detailConcaveIDs[i]);
            groupAreas = Array.concat(groupAreas, detailAreas[i]);
            groupPerimeters = Array.concat(groupPerimeters, detailPerimeters[i]);
            groupCentroidsX = Array.concat(groupCentroidsX, centroidX);
            groupCentroidsY = Array.concat(groupCentroidsY, centroidY);
            groupSolidities = Array.concat(groupSolidities, detailSolidities[i]);
            groupCircularities = Array.concat(groupCircularities, detailCircularities[i]);
            groupAspectRatios = Array.concat(groupAspectRatios, detailAspectRatios[i]);
        }
    }

    print("DEBUG: Found " + groupParticleIDs.length + " concave areas for particles " + startParticle + "-" + endParticle);

    // Populate the table with all data at once
    if (groupParticleIDs.length > 0) {
        Table.setColumn("Particle_ID", groupParticleIDs);
        Table.setColumn("Concave_Area_ID", groupConcaveIDs);
        Table.setColumn("Area_" + unit + "²", groupAreas);
        Table.setColumn("Perimeter_" + unit, groupPerimeters);
        Table.setColumn("Centroid_X_" + unit, groupCentroidsX);
        Table.setColumn("Centroid_Y_" + unit, groupCentroidsY);
        Table.setColumn("Solidity", groupSolidities);
        Table.setColumn("Circularity", groupCircularities);
        Table.setColumn("Aspect_Ratio", groupAspectRatios);
        Table.update;
    }
}

// Helper function to get minimum of two values
function minOf(a, b) {
    if (a < b) {
        return a;
    } else {
        return b;
    }
}

// Helper function to get ceiling of division
function ceil(value) {
    intValue = floor(value);
    if (value > intValue) {
        return intValue + 1;
    } else {
        return intValue;
    }
}

// Function to create visual overlay showing concave areas
function createVisualOverlay() {
    print("");
    print("=== CREATING VISUAL OVERLAY ===");

    // Select working image for overlay
    selectImage(workingID);

    // Remove existing overlay
    run("Remove Overlay");

    // Create overlay showing particles and their concave areas
    concaveIndex = 0;

    for (p = 0; p < nParticles; p++) {
        // Show particle outline
        roiManager("select", p);
        roiManager("Set Color", "yellow");
        roiManager("Set Line Width", 2);
        run("Add Selection...");

        // Add particle number label
        Roi.getBounds(x, y, width, height);
        centerX = x + width/2;
        centerY = y + height/2;

        setColor("white");
        setFont("SansSerif", 12, "bold");
        makeText("P" + (p+1), centerX-10, centerY-15);
        run("Add Selection...");

        // Add concave area count label
        setColor("cyan");
        makeText("C:" + concaveAreaCounts[p], centerX-10, centerY+5);
        run("Add Selection...");

        // Mark concave area centroids
        for (c = 0; c < concaveAreaCounts[p]; c++) {
            if (concaveIndex < detailCentroids.length / 2) {
                concaveX = detailCentroids[concaveIndex * 2];
                concaveY = detailCentroids[concaveIndex * 2 + 1];

                // Create small circle at concave area centroid
                makeOval(concaveX-3, concaveY-3, 6, 6);
                setColor("red");
                run("Add Selection...");

                // Add concave area number
                setColor("white");
                setFont("SansSerif", 8, "bold");
                makeText("" + (c+1), concaveX-2, concaveY-2);
                run("Add Selection...");

                concaveIndex++;
            }
        }
    }

    run("Select None");
    print("Visual overlay created showing particles (yellow) and concave areas (red dots)");
    print("P# = Particle number, C:# = Concave area count");
}
